import { headers } from 'next/headers';
import { NextRequest } from 'next/server';

import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendDeleteCartSummary,
  backendGetCartSummaryLegacy,
  backendUpdateCartSummary,
} from '~/lib/backend/checkout/cart-summary';
import logger from '~/lib/helpers/logger';
import { getUserSessionId } from '~/lib/utils/api';
import { getStringifiedParams } from '~/lib/utils/routes';

import { CartSummaryApiParams } from './route.types';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<CartSummaryApiParams> },
) {
  await backendBootstrap();

  const { id } = await params;
  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { ...rest } = getStringifiedParams(query);

  const res = await backendGetCartSummaryLegacy(
    { id, query: rest },
    request,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<CartSummaryApiParams> },
) {
  await backendBootstrap();

  const headersList = await headers();
  const userSessionId = getUserSessionId(headersList);

  if (!userSessionId) {
    logger.error('Invalid authentication');
    return new Response(null, { status: 204 });
  }

  const { id } = await params;
  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { ...rest } = getStringifiedParams(query);

  const body = await request.json();

  const res = await backendUpdateCartSummary(
    {
      id,
      userTime: rest.userTime,
      vwo_user: rest.vwo_user,
      input: {
        sessionId: userSessionId,
        ...body,
      },
    },
    request,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<CartSummaryApiParams> },
) {
  await backendBootstrap();

  const { id } = await params;
  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { ...rest } = getStringifiedParams(query);

  const res = await backendDeleteCartSummary({ id, query: rest }, request);

  if (res.isSuccess) {
    return new Response(null, { status: 204 });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}
